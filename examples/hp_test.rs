use anyhow::Result;
use pkarr::PublicKey;

#[tokio::main]
async fn main() -> Result<()> {
    println!("Testing HP lib import...");

    // Try to import the module
    use iroh_topic_tracker::integrations::hp::HpClient;
    println!("✓ Import successful");

    // Try to create client
    let _client = HpClient::new()?;
    println!("✓ Client creation successful");

    // Test public key parsing
    let test_key = "ej1b6zrud5q1ufrib1dkzx38hx473wyx5o4ouj4qpu5ut1pegmfo";
    println!("Testing public key parsing: {}", test_key);
    let _pub_key = PublicKey::try_from(test_key)?;
    println!("✓ Public key parsing successful");

    Ok(())
}
